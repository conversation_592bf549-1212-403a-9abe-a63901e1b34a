<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;

/**
 * SwbUsersFixture
 */
class SwbUsersFixture extends TestFixture
{
    /**
     * Table name
     */
    public $table = 'swb_users';

    /**
     * Fields
     */
    public $fields = [
        'id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '', 'autoIncrement' => true, 'precision' => null],
        'authority_id' => ['type' => 'integer', 'length' => null, 'unsigned' => false, 'null' => false, 'default' => null, 'comment' => '権限（100:admin, 101:admin_confirmer）', 'precision' => null],
        'email' => ['type' => 'string', 'length' => 255, 'null' => false, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => 'メールアドレス（ログイン用）', 'precision' => null],
        'password' => ['type' => 'string', 'length' => 255, 'null' => true, 'default' => null, 'collate' => 'utf8mb4_unicode_ci', 'comment' => 'パスワード（ハッシュ化）', 'precision' => null],
        'created' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '作成日時'],
        'modified' => ['type' => 'datetime', 'length' => null, 'precision' => null, 'null' => false, 'default' => 'CURRENT_TIMESTAMP', 'comment' => '更新日時'],
        'deleted' => ['type' => 'datetime', 'length' => null, 'null' => true, 'default' => null, 'comment' => '削除日時(論理削除)', 'precision' => null],
        '_indexes' => [
            'idx_swb_users_authority_id' => ['type' => 'index', 'columns' => ['authority_id'], 'length' => []],
            'idx_swb_users_created' => ['type' => 'index', 'columns' => ['created'], 'length' => []],
        ],
        '_constraints' => [
            'primary' => ['type' => 'primary', 'columns' => ['id'], 'length' => []],
            'uk_swb_users_email' => ['type' => 'unique', 'columns' => ['email'], 'length' => []],
        ],
        '_options' => [
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci'
        ],
    ];

    /**
     * Init method
     */
    public function init(): void
    {
        $this->records = [
            [
                'id' => 1,
                'authority_id' => 100, // admin
                'email' => '<EMAIL>',
                'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'created' => '2024-01-01 10:00:00',
                'modified' => '2024-01-01 10:00:00',
                'deleted' => null,
            ],
            [
                'id' => 2,
                'authority_id' => 101, // admin_confirmer
                'email' => '<EMAIL>',
                'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
                'created' => '2024-01-02 11:00:00',
                'modified' => '2024-01-02 11:00:00',
                'deleted' => null,
            ],
            [
                'id' => 3,
                'authority_id' => 100, // admin
                'email' => '<EMAIL>',
                'password' => null, // Kurocoユーザー（パスワードなし）
                'created' => '2024-01-03 12:00:00',
                'modified' => '2024-01-03 12:00:00',
                'deleted' => null,
            ],
        ];
        parent::init();
    }
}
