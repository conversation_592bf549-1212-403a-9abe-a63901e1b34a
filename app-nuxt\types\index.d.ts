interface Window {
    $ts: (message: string) => void;
}

type ObjType = {
    [key: string]: string | number | undefined | boolean | null;
    /** こうやってコメント書くとエディタが読む */
    flag?: boolean;
};

type TFormValue = string | number;
type TValidationSuccess = boolean | string;
type TValidationFunction = (v: TFormValue) => TValidationSuccess;

type ObjArray = Array<ObjType>;

interface ISample {
    method: (val: string) => string;
}

type TListItem = {
    value: string | number;
    label: string;
};

type TList = TListItem[];

type TJapaneseKeyMap = {
    name1: string;
    name2: string;
    name1_hurigana: string;
    name2_hurigana: string;
    email: string;
    login_pwd?: string;
    newsletter_opt_in: string;
    zip_code: string;
    tdfk_cd: string;
    address1: string;
    address2: string;
    address3: string;
    tel: string;
    child_name?: string;
    child_sex?: string;
    child_birthdate?: string;
    custom_budget?: string;
    custom_catalog_request_triggers?: string;
    custom_key_points?: string;
};

type ValidationError = {
    code: string;
    message: string;
    field: string;
};

type TMemberUpdate = {
    member_id: number;
    name1: string;
    name2: string;
    name1_hurigana: string;
    name2_hurigana: string;
    email: string;
    zip_code: string;
    tdfk_cd: string;
    address1: string;
    address2: string;
    address3: string;
    tel: string;
    email_send_ng_flg: boolean;
};

type TMember = {
    member_id: number;
    name1: string;
    name2: string;
    name1_hurigana: string;
    name2_hurigana: string;
    email: string;
    zip_code: string;
    tdfk_cd: string;
    address1: string;
    address2: string;
    address3: string;
    tel: string;
    email_send_ng_flg: boolean;
    maker_id: string;
};

type TMemberResponse = {
    member: TMember;
};

type TRandselOrdersResponse = {
    orders: TRandselOrder[];
};

type TRandselOrdersResponse = {
    randsel_orders: TRandselOrder[];
};

type TOrderDetail = {
    order_detail_id: number;
    product_id: number;
    product_name: string;
    product_memo: string;
};

type TOrder = {
    ec_order_id: number;
    member_id: number;
    order_nm01: string;
    order_nm02: string;
    order_nm01_kana: string;
    order_nm02_kana: string;
    memo01: string;
    billing_date: string;
    tdfk_cd: string;
    approval_status?: string;
    payment_total: number;
    order_email: string;
    order_zip: string;
    order_addr01: string;
    order_addr02: string;
    order_addr03: string;
    order_tel: string;
    send_email_ng_flag: boolean;
    inquiry_body: string;
    order_details: TOrderDetail[];
};

type TRandselOrder = {
    id: number;
    maker_id: number;
    member_id: number;
    general_user_id: number;
    product_id: number;
    product_name: string;
    price: number;
    status: number;
    status_modified: string | null;
    approval_type: string | null;
    is_confirmed: boolean;
    confirmed: string | null;
    name1: string;
    name2: string;
    name1_hurigana: string;
    name2_hurigana: string;
    zip_code: string;
    tdfk_cd: string;
    address1: string;
    address2: string;
    address3: string;
    tel: string;
    email: string;
    email_send_ng_flg: boolean;
    survey_json: string;
    created: string;
    modified: string;
};
