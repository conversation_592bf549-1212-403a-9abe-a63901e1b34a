<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\Utility\Security;
use App\Model\Entity\SwbUserToken;

/**
 * SWB管理者ユーザートークンテーブル
 * 
 * @property \App\Model\Table\SwbUsersTable&\Cake\ORM\Association\BelongsTo $SwbUsers
 * @method \App\Model\Entity\SwbUserToken newEmptyEntity()
 * @method \App\Model\Entity\SwbUserToken newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\SwbUserToken[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\SwbUserToken get($primaryKey, $options = [])
 * @method \App\Model\Entity\SwbUserToken findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\SwbUserToken patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\SwbUserToken[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\SwbUserToken|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\SwbUserToken saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 */
class SwbUserTokensTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('swb_user_tokens');
        $this->setDisplayField('token');
        $this->setPrimaryKey('id');

        $this->belongsTo('SwbUsers', [
            'foreignKey' => 'swb_user_id',
            'joinType' => 'INNER',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('id')
            ->allowEmptyString('id', null, 'create');

        $validator
            ->integer('swb_user_id')
            ->requirePresence('swb_user_id', 'create')
            ->notEmptyString('swb_user_id');

        $validator
            ->scalar('token')
            ->maxLength('token', 255)
            ->requirePresence('token', 'create')
            ->notEmptyString('token')
            ->add('token', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->scalar('type')
            ->maxLength('type', 50)
            ->requirePresence('type', 'create')
            ->notEmptyString('type')
            ->inList('type', [
                SwbUserToken::TYPE_PASSWORD_RESET,
                SwbUserToken::TYPE_API_ACCESS,
                SwbUserToken::TYPE_EMAIL_VERIFICATION
            ]);

        $validator
            ->dateTime('expires')
            ->requirePresence('expires', 'create')
            ->notEmptyDateTime('expires');

        return $validator;
    }

    /**
     * トークンを生成
     */
    public function generateToken(): string
    {
        return bin2hex(Security::randomBytes(32));
    }

    /**
     * 有効なトークンを検索
     */
    public function findValidToken(string $token, string $type = null)
    {
        $query = $this->find()
            ->contain(['SwbUsers'])
            ->where([
                'SwbUserTokens.token' => $token,
                'SwbUserTokens.expires >' => FrozenTime::now()
            ]);

        if ($type) {
            $query->where(['SwbUserTokens.type' => $type]);
        }

        return $query->first();
    }

    /**
     * 期限切れトークンを削除
     */
    public function cleanupExpiredTokens(): int
    {
        return $this->deleteAll([
            'expires <' => FrozenTime::now()
        ]);
    }

    /**
     * ユーザーの特定タイプのトークンを無効化
     */
    public function invalidateUserTokens(int $userId, string $type): int
    {
        return $this->deleteAll([
            'swb_user_id' => $userId,
            'type' => $type
        ]);
    }

    /**
     * パスワードリセットトークンを作成
     */
    public function createPasswordResetToken(int $userId, int $expirationHours = 24)
    {
        // 既存のパスワードリセットトークンを無効化
        $this->invalidateUserTokens($userId, SwbUserToken::TYPE_PASSWORD_RESET);

        $token = $this->newEntity([
            'swb_user_id' => $userId,
            'token' => $this->generateToken(),
            'type' => SwbUserToken::TYPE_PASSWORD_RESET,
            'expires' => FrozenTime::now()->addHours($expirationHours)
        ]);

        return $this->save($token);
    }

    /**
     * 特定のトークンを無効化（削除）
     */
    public function invalidateToken(string $token): bool
    {
        try {
            $tokenEntity = $this->find()
                ->where([
                    'token' => $token,
                    'expires >' => FrozenTime::now()
                ])
                ->first();

            if ($tokenEntity) {
                return (bool)$this->delete($tokenEntity);
            }

            return false;
        } catch (\Exception $e) {
            Log::error("SwbUserTokensTable: Error invalidating token: " . $e->getMessage());
            return false;
        }
    }
}
