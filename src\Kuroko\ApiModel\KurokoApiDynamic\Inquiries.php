<?php

namespace App\<PERSON>roko\ApiModel\KurokoApiDynamic;

use App\Kuroko\ApiModel\KurokoApiDynamicModel;
use App\Kuroko\Entity\Inquiry;
use Cake\Log\Log;
use Cake\Utility\Hash;

class Inquiries extends KurokoApiDynamicModel
{
    /**
     * @param array $data
     * @return array
     */
    public function listForMaker(array $data = []): array
    {
        $this->_setSysUserToken();
        $response = $this->get($this->getEndPoint("inquiries"));
        if ($response->isSuccess() && $body = $response->getStringBody()) {
            if ($data = json_decode($body, true)) {
                if (empty(Hash::get($data, "errors", [])) && Hash::check($data, "list")) {
                    return Inquiry::creates(Hash::get($data, "list"));
                } else {
                    Log::error($response->getStringBody());
                }
            }
        }
        Log::debug(__METHOD__ . " request data: " . json_encode($data));
        Log::debug(__METHOD__ . " response: " . $response->getStringBody());
        return [];
    }
}
