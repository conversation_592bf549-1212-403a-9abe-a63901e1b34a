<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\ORM\Entity;

/**
 * SWB管理者ユーザーエンティティ
 * 
 * @property int $id
 * @property int $authority_id
 * @property string $email
 * @property string|null $password
 * @property \Cake\I18n\FrozenTime|null $deleted
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 * @property \App\Model\Entity\SwbUserToken[] $swb_user_tokens
 */
class SwbUser extends Entity
{
    protected $_accessible = [
        'authority_id' => true,
        'email' => true,
        'password' => true,
        'deleted' => true,
        'created' => true,
        'modified' => true,
        'swb_user_tokens' => true,
    ];

    protected $_hidden = [
        'password',
    ];

    // パスワードのハッシュ化
    protected function _setPassword($password)
    {
        if ($password) {
            return (new DefaultPasswordHasher())->hash($password);
        }
        return null;
    }

    // Kurocoユーザーかどうかを判定
    public function isKurocoUser(): bool
    {
        return $this->password === null;
    }

    // 権限レベルの判定
    public function isAdmin(): bool
    {
        return $this->authority_id === 100;
    }

    public function isAdminConfirmer(): bool
    {
        return $this->authority_id === 101;
    }

    // 権限名を取得
    public function getAuthorityName(): string
    {
        switch ($this->authority_id) {
            case 100:
                return 'admin';
            case 101:
                return 'admin_confirmer';
            default:
                return 'unknown';
        }
    }
}

