<script setup lang="ts">
import SchoolBagFormLogined from "~~/src/form/SchoolBagFormLogined";
import { reactive, ref } from "vue";
import { useRuntimeConfig } from "#app";
import Orders from "~/src/models/Orders";
import TheMemberDetail from "~~/components/my/member/TheMemberDetail.vue";
import TheAffiliateAd from "~/components/ad/TheAffiliateAd.vue";

const props = defineProps({
    productsData: {
        type: Array,
        required: true,
    },
    // memberData: {
    //     type: Object,
    //     required: true,
    // },
    orderData: {
        type: Array,
        required: true,
    },
});
//  http://localhost:3000/catalog/form/randsel
const schoolBagForm = reactive(
    new SchoolBagFormLogined(
        props.productsData as [],
        props.orderData as [],
        // props.memberData.data,
    ),
);

const stepEdit = "edit";
const stepConf = "conf";
const stepComplete = "complete";
const text = ref(
    "カタログは各メーカーから直接発送されます。\n※発送時期はメーカーによって異なります。",
);

const isLoading = ref(false);
const step = ref(stepEdit);
const config = useRuntimeConfig();
const formRef = ref();

const toConf = async (): Promise<void> => {
    const countValid = schoolBagForm.orderProductIdsValidate();

    step.value = countValid ? stepConf : step.value;

    if (!countValid) {
        // バリデーションが失敗した場合、ページのトップにスクロールする
        window.scrollTo({
            top: 0,
            behavior: "smooth", // スムーズにスクロールする場合
        });
    }
};

const toEdit = (): void => {
    step.value = stepEdit;
};

const toComplete = (): void => {
    isLoading.value = true;
    Orders.create(config)
        .add(schoolBagForm.data)
        .then((success) => {
            if (success) {
                step.value = stepComplete;
            } else {
                console.log("error");
            }
            isLoading.value = false;
        });
};
</script>

<template>
    <div>
        <!-- {{ schoolBagForm.ordered_product_ids }}
        {{ schoolBagForm.order_product_ids }}
        {{ config }}
        {{ schoolBagForm.valid }} -->
        <div class="text-h6 font-weight-bold pa-4">
            2026年度ランドセルカタログ一括請求申込（追加カタログ申込）
        </div>
        <v-divider class="my-4"></v-divider>
        <template v-if="isLoading">
            <v-progress-circular indeterminate></v-progress-circular>
        </template>
        <template v-else>
            <template v-if="step === stepEdit">
                <v-form
                    ref="formRef"
                    v-model="schoolBagForm.valid"
                    validate-on="blur"
                >
                    <v-container class="border-md border-primary pa-3">
                        <v-row v-if="schoolBagForm.errors.order_product_ids">
                            <v-alert type="error">
                                {{ schoolBagForm.errors.order_product_ids }}
                            </v-alert>
                        </v-row>
                        <v-row>
                            <v-col cols="12" class="pa-0">
                                <div
                                    class="text-h6 text-center font-weight-bold bg-primary text-white pa-1"
                                >
                                    カタログを選ぶ
                                </div>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col
                                v-for="product of schoolBagForm.products"
                                :key="product.product_id"
                                class="coverme-catalog-checkbox"
                                cols="12"
                                md="6"
                            >
                                <v-card
                                    variant="outlined"
                                    class="coverme-card fill-height d-flex flex-column"
                                >
                                    <v-checkbox
                                        v-model="
                                            schoolBagForm.order_product_ids
                                        "
                                        :value="product.product_id"
                                        hide-details
                                        class="font-weight-bold"
                                        :disabled="
                                            schoolBagForm.ordered_product_ids.includes(
                                                product.product_id,
                                            ) ||
                                            product.product_id == 41209 ||
                                            product.product_id == 41213 ||
                                            product.product_id == 41221
                                        "
                                    >
                                        <template #label>
                                            <div>
                                                <span
                                                    v-if="
                                                        product.product_id ==
                                                        41219
                                                    "
                                                    class="text-red bg-yellow"
                                                    >【恐竜好きならチェック】<br
                                                /></span>
                                                <span
                                                    v-if="
                                                        product.product_id ==
                                                        41221
                                                    "
                                                    class="text-red bg-yellow"
                                                    >【東海エリアの方はチェック】<br
                                                /></span>
                                                <span
                                                    v-if="
                                                        product.product_id ==
                                                        41215
                                                    "
                                                    class="text-red bg-yellow"
                                                    >【ふわりぃは軽い！最軽量880g～】<br
                                                /></span>

                                                {{ product.product_name }}
                                            </div>
                                        </template>
                                    </v-checkbox>
                                    <div class="mx-4 d-flex justify-center">
                                        <div style="width: 150px">
                                            <v-img
                                                :src="product.image_url"
                                                width="150"
                                                aspect-ratio="1"
                                            >
                                            </v-img>
                                        </div>
                                    </div>
                                    <div class="ma-4 text-pre-wrap">
                                        {{ product.product_contents }}
                                    </div>
                                    <div
                                        class="ma-4 mt-auto text-center text-primary"
                                    >
                                        {{ product.memo }}
                                    </div>
                                </v-card>
                            </v-col>
                        </v-row>
                        <v-row class="ma-0 mt-3">
                            <div class="text-body-2 text-pre-wrap">
                                {{ text }}
                            </div>
                        </v-row>
                    </v-container>
                    <v-divider></v-divider>

                    <v-container>
                        <v-row justify="center">
                            <v-col cols="12" md="6">
                                <v-btn
                                    block
                                    color="primary"
                                    rounded="xl"
                                    size="large"
                                    @click="toConf"
                                >
                                    次へ
                                </v-btn>
                            </v-col>
                        </v-row>
                    </v-container>
                </v-form>
            </template>
            <template v-else-if="step === stepConf">
                <v-container class="border-md border-primary pa-3 mb-4">
                    <v-row>
                        <v-col cols="12" class="pa-0">
                            <div
                                class="text-h6 text-center font-weight-bold bg-primary text-white pa-1"
                            >
                                選択したカタログ
                            </div>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-list :lines="false">
                            <template
                                v-for="product of schoolBagForm.products"
                                :key="product.product_id"
                            >
                                <v-list-item
                                    v-if="
                                        schoolBagForm.add_order_product_ids.includes(
                                            product.product_id,
                                        )
                                    "
                                    class="text-h3"
                                    :subtitle="product.product_name"
                                ></v-list-item>
                            </template>
                        </v-list>
                    </v-row>
                </v-container>
                <v-container>
                    <the-member-detail :show-update-dialog="false">
                    </the-member-detail>
                    <v-row justify="center">
                        <v-col cols="12" md="4">
                            <v-btn
                                id="covermeSubmit"
                                block
                                color="primary"
                                rounded="xl"
                                size="large"
                                @click="toComplete"
                            >
                                申込を確定する
                            </v-btn>
                        </v-col>
                        <v-col cols="12" md="4">
                            <v-btn
                                block
                                color="white"
                                rounded="xl"
                                size="large"
                                class="fix-btn"
                                @click="toEdit"
                            >
                                修正する
                            </v-btn>
                        </v-col>
                    </v-row>
                </v-container>
            </template>
            <template v-else-if="step === stepComplete">
                <v-container>
                    <!-- <p class="text-h5 font-weight-bold mb-10">
                        カタログの申し込みはまだ完了していません
                    </p> -->
                    <p class="text-body-1">
                        追加のカタログのお申込が完了しました。<br /><br />
                        ありがとうございます。カタログ到着を楽しみにお待ちください。<br /><br />
                        ※カタログの発送時期はメーカーにより異なります。<br /><br />
                        お申込いただいたカタログは
                        <NuxtLink to="/member/account/"> マイページ </NuxtLink>
                        でご確認いただけます。<br /><br />
                    </p>
                    <v-divider></v-divider>
                    <the-affiliate-ad></the-affiliate-ad>
                </v-container>
            </template>
        </template>
    </div>
</template>

<style scoped lang="scss">
.test {
    color: $error-color;
}

.coverme-card {
    border-radius: 14px;
    border: 2px solid rgb(255 217 222);
}
.fix-btn {
    border: 2px solid #f4f4f4;
    color: #7a7a7a !important;
}
</style>
