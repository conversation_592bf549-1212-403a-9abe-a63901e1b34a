export class Product {
    private readonly _product_id: number;
    private readonly _product_name: string;
    private readonly _topics_name: string;
    private readonly _product_data: TProductData;
    constructor(data: TProduct) {
        this._product_id = data.product_id;
        this._product_name = data.product_name;
        this._topics_name = data.topics_name;
        this._product_data = data.product_data;
    }

    static creates(products: TProduct[]): Product[] {
        return products.map((product: TProduct) => Product.create(product));
    }

    static create(product: TProduct): Product {
        return new Product(product);
    }

    get data(): TProduct {
        return {
            product_id: this.product_id,
            product_name: this.product_name,
            topics_name: this.topics_name,
            product_data: this.product_data,
        };
    }

    // get mid(): string {
    //     // for (const extColumn of this.product_data.ext_columns.straight) {
    //     //     if (extColumn.ext_slug === "mid") {
    //     //         return extColumn.value;
    //     //     }
    //     // }

    //     const ext = this.product_data.ext;
    //     try {
    //         const mid = JSON.parse(ext);
    //         if (Object.prototype.hasOwnProperty.call(mid, "1")) {
    //             return mid["1"];
    //         }
    //     } catch (error) {
    //         console.error("product_data.ext JSONパース失敗:", error);
    //     }
    //     console.error("No Mid");
    //     return "";
    // }

    get memo(): string {
        const ext = this.product_data.ext;
        try {
            const extData = JSON.parse(ext);
            if (Object.prototype.hasOwnProperty.call(extData, "4")) {
                return extData["4"];
            }
        } catch (error) {
            console.error("product_data.ext JSONパース失敗:", error);
        }
        console.error("No memo");
        return "";
    }

    get image_url(): string {
        const ext = this.product_data.ext;
        try {
            const extData = JSON.parse(ext);
            if (Object.prototype.hasOwnProperty.call(extData, "2")) {
                return extData["2"];
            }
        } catch (error) {
            console.error("product_data.ext JSONパース失敗:", error);
        }
        console.error("No Image");
        return "";
    }

    get product_data(): TProductData {
        return this._product_data;
    }

    get product_contents(): string {
        return this.product_data.contents;
    }

    get product_id(): number {
        return this._product_id;
    }

    get product_id_string(): string {
        return String(this.product_id);
    }

    get product_name(): string {
        return this._product_name;
    }

    get topics_name(): string {
        return this._topics_name;
    }
}
