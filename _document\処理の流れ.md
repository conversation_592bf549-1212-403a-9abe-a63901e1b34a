# データベーステーブル一覧

## ユーザー関連テーブル

| テーブル名 | 説明 |
|------------|------|
| GENERAL_USERS | 一般ユーザーの基本情報を管理するテーブル。ログイン認証情報（メールアドレス、パスワード）やアカウント状態を保持する。 |
| SWB_USERS | SWB管理者ユーザーの基本情報を管理するテーブル。ログイン認証情報（メールアドレス、パスワード）や権限レベルを保持する。 |
| MAKER_USERS | メーカーユーザーの基本情報を管理するテーブル。メーカー専用のログイン認証情報（メールアドレス、パスワード）やアカウント状態を保持する。 |
| USER_PROFILES | ユーザーの個人情報を暗号化して保存するテーブル。氏名、住所、連絡先などの個人情報を管理する。 |
| USER_SURVEYS | ユーザーのアンケート回答データを保存するテーブル。子供の性別、予算、購入のきっかけなどの情報を管理する。 |
| USER_TOKENS | 一般ユーザーのパスワードリセットやAPI認証などに使用するトークン情報を管理するテーブル。トークンの種類と有効期限を保持する。 |
| SWB_USER_TOKENS | SWB管理者ユーザーのトークン情報を管理するテーブル。トークンの種類と有効期限を保持する。 |
| MAKER_USER_TOKENS | メーカーユーザーのトークン情報を管理するテーブル。トークンの種類と有効期限を保持する。 |
| TEMPORARY_REGISTRATIONS | ユーザー仮登録情報を管理するテーブル。メール認証前の一時データを保存し、認証完了後に正式なユーザーデータとして移行される。有効期限付きで管理される。 |

## 注文・請求関連テーブル

| テーブル名 | 説明 |
|------------|------|
| RANDSEL_ORDERS | ランドセルの注文情報を管理するテーブル。注文状態、承認情報、顧客情報（暗号化）を保持する。 |
| RANDSEL_INVOICES | メーカーへの請求情報を管理するテーブル。月次の請求金額、数量を集計し、請求書発行の基礎データとなる。 |
| RANDSEL_INVOICE_ADJUSTMENTS | 請求金額の調整情報を管理するテーブル。調整理由、金額、数量を記録し、請求金額の修正に使用する。 |
| RANDSEL_INVOICE_ADJUSTMENT_HISTORIES | 請求調整の変更履歴を記録するテーブル。誰がいつどのような変更を行ったかの監査証跡を保持する。 |

## メーカー・商品関連テーブル

| テーブル名 | 説明 |
|------------|------|
| MAKERS | メーカー基本情報を管理するテーブル。請求先情報、連絡先、請求サイクルなどのメーカー情報を保持する。 |
| MAKER_STORES | メーカーの店舗情報を管理するテーブル。店舗の住所、営業時間、アクセス情報などを保持する。 |
| BRANDS | ブランド情報を管理するテーブル。ブランド名、説明、ターゲット層、価格帯などの詳細情報を保持する。 |
| PRODUCTS | 商品（ランドセルカタログ）情報を管理するテーブル。表示状態、商品説明、画像・PDFのURL、価格帯、重さ帯、素材などを保持する。 |
| BUDGETS | 商品の掲載条件を管理するテーブル。カタログタイプ、単価、予算数量、掲載期間などを定義する。 |
| EXHIBITIONS | 展示会情報を管理するテーブル。開催日時、会場、定員などの展示会に関する情報を保持する。 |
| EXHIBITION_MAKERS | 展示会とメーカーの関連を管理するテーブル。どの展示会にどのメーカーが参加するかを定義する。 |
| LIVE_STREAMS | ライブ配信情報を管理するテーブル。配信日時、プラットフォーム、URLなどのライブ配信に関する情報を保持する。 |
| LIVE_STREAM_MAKERS | ライブ配信とメーカーの関連を管理するテーブル。どのライブ配信にどのメーカーが参加するかを定義する。 |


## 一般ユーザーカタログ申し込み処理の流れ

### 一般ユーザーの操作フロー（その1）: 新規登録

1. **カタログ選択と個人情報入力**:
   - ユーザーがカタログを選択し、個人情報とアンケートを入力
   - 次へ確認ページへ遷移するタイミングで`GENERAL_USERS` テーブルに既存ユーザーの`mail`をチェック
   - `TEMPORARY_REGISTRATIONS` テーブルに情報を保存
   - 認証トークンを生成し、有効期限（24時間）を設定

2. **仮登録完了と本人確認メール送信**:
   - 仮登録完了画面を表示
   - 認証トークン付きのリンクを含む本人確認メールを送信

3. **本登録処理**:
   - ユーザーがメール内のリンクをクリック
   - トークンを検証し、`TEMPORARY_REGISTRATIONS` から情報を取得
   - `GENERAL_USERS` テーブルに新規ユーザーを作成
   - `USER_PROFILES` テーブルにプロフィール情報を作成
   - `USER_SURVEYS` テーブルにアンケート情報を保存
   - 選択したカタログの申し込み処理を `RANDSEL_ORDERS` テーブルに記録
   - `TEMPORARY_REGISTRATIONS` の `is_verified` フラグを更新

4. **本登録完了**:
   - 本登録完了と申し込み完了の画面を表示
   - 本登録完了メールを送る

### 一般ユーザーの操作フロー（その2）: 追加カタログ申し込み

1. **ユーザーログイン**:
   - メールアドレスとパスワードでログイン

2. **追加カタログ選択と申し込み**:
   - 追加カタログを選択
   - 申し込み情報を確認
   - `RANDSEL_ORDERS` テーブルに新規レコードを作成

3. **申し込み完了**:
   - 追加カタログ申し込み完了画面を表示

## 仮登録の扱い

### 仮登録の管理方法

- 仮登録情報は `TEMPORARY_REGISTRATIONS` テーブルで管理
- 各仮登録には24時間の有効期限が設定
- **同一メールアドレス**で複数回仮登録が行われた場合、その都度**新しいレコード**が追加されます
- プライバシー保護のため、期限切れデータの `email` 、`mask_password` 、`profile_data`、`survey_data` カラムは日次バッチ処理によって自動的にクリア（null化）されます

### 重複仮登録の処理ロジック

ユーザーが複数の仮登録リンクを持っている場合の動作は以下の通り：

1. `GENERAL_USERS` テーブルの「メールアドレス」カラムにはユニーク制約が設定
2. ユーザーが任意の仮登録確認リンクをクリックすると、対応する仮登録データが `GENERAL_USERS` テーブルにコピーされ、本登録が完了
3. その後、同じユーザーが別の仮登録確認リンクをクリックしても、メールアドレスのユニーク制約により登録は拒否され、エラーメッセージが表示されます


## アンケートの扱い

- アンケートデータは `USER_SURVEYS` テーブルで管理
- 1ユーザーに対して0または1つのアンケートという関係を表現
- 仮登録時に入力されたアンケートデータは、本登録時に `USER_SURVEYS` テーブルに移行

## 注文の扱い

### 注文データの管理方法

- 注文データは `RANDSEL_ORDERS` テーブルで管理
- Kuroco脱却後のシステムでは、注文者の詳細情報は `USER_PROFILES` テーブルに格納
- アンケート回答データは `USER_SURVEYS` テーブルに格納

### 重複注文の防止機能

システムでは「一人につき同一商品は一回のみ購入可能」という仕様を実装する。

この制約を実現するため、`RANDSEL_ORDERS` テーブルには以下のユニーク制約が設定：
- 「商品ID (`product_id`)」と「ユーザーID (`user_id`)」の組み合わせに対するユニークキー

---

## カタログ掲載制御の流れ

### 掲載開始から終了までの流れ

1. **カタログ情報登録**:
   - メーカーからカタログ情報を受け取る
   - `BRANDS` テーブルにブランド情報を登録
   - `PRODUCTS` テーブルに商品情報を登録
      - 商品の基本情報（名称、説明、画像URL、PDFカタログURL等）を設定
   - テーブルと関連付け
      例：
      - メーカー：ららや
      - ブランド：ららちゃんランドセル
      - カタログ：2026年ららちゃんランドセル

2. **掲載条件設定**:
   - `BUDGETS` テーブルに掲載条件を登録
      - カタログタイプ（紙/デジタル）の指定
      - 単価の設定
      - 予算数量の設定
      - 予算開始日・終了日の設定

3. **掲載開始**:
   - 設定された開始日になると自動的に掲載開始
   - `PRODUCTS` テーブルの `is_display` を「`true`」に更新
   - ウェブサイト上でカタログが閲覧・申込可能になる

4. **申し込みフォーム掲載タイプ（紙、PDF、売り切れ）の表示制御**:
   - 制御タイミング：申し込みフォーム表示時
   - 対象カタログ：現在表示可能なカタログ
   - 処理フロー
      ```mermaid
      graph TD
         A[1.　申し込みフォーム表示] --> B[2.　BUDGETSテーブルから現在時刻に適用される予算上限と各条件（カタログ提供タイプ、予算開始日、優先順位）を取り出す];
         B --> C[3.　No.2から取得した予算開始日から現在時刻まで各タイプの注文件数を算出する];
         C --> D[4.　No.3の注文件数とNo.2の予算上限を比較して、優先順位が降順で判断結果を並ぶ];
         D --> E[5.　最上位の判断結果により掲載タイプ（紙、PDF、売り切れ）の表示を制御する];
         E --> G[6.　処理完了];
      ```
      <details><summary>判断用SQLの例</summary>
      
      ```
        WITH active_conditions AS (
            SELECT 
                id,
                product_id,
                `type`,
                budget_quantity,
                start_date,
                priority
            FROM 
                BUDGETS
            WHERE 
                is_active = true
                AND CURRENT_TIMESTAMP >= start_date
                AND CURRENT_TIMESTAMP <= end_date
            ORDER BY 
                priority DESC, start_date DESC
        ),
        ordered_counts AS (
            SELECT 
                p.id,
                p.`type`,
                p.budget_quantity,
                p.priority,
                p.start_date,
                (
                    SELECT COUNT(*) 
                    FROM RANDSEL_ORDERS o 
                    WHERE 
                        o.product_id = p.product_id
                        AND o.type = p.type
                        AND o.created >= p.start_date
                ) AS ordered_quantity,
                ROW_NUMBER() OVER (PARTITION BY p.type ORDER BY p.priority DESC, p.id ASC) AS rn
            FROM 
                active_conditions p
        )
        SELECT 
            id,
            `type`,
            budget_quantity,
            ordered_quantity,
            priority,
            start_date,
            rn
        FROM 
            ordered_counts
        WHERE 
            ordered_quantity < budget_quantity
        ORDER BY 
            priority DESC, rn ASC
        LIMIT 1
        ;

      ```
      </details>

   - パターン１（紙とデジタルの併用）：

      | カタログタイプ | type | budget_quantity | is_active | start_date | end_date | priority |
      |------------|------|----------------|-----------|------------|----------|----------|
      | 紙カタログ用 | 1（紙） | 60 | true（有効） | 2025-05-01 | 2025-05-31 | 2 |
      | デジタルカタログ用 | 2（デジタル） | 999999（無限） | true（有効） | 2025-05-01 | 2025-05-31 | 1 |

      - **ケースA**：
         条件:
         - `CURRENT_TIMESTAMP`: `2025-05-25 10:00:00`
         - `RANDSEL_ORDERS` の注文件数:
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=1` (紙)の注文件数: : **59**件
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=2` (デジタル)の注文件数: 0件

         処理:
         - 処理フロー②：紙とデジタルの両方が有効期間内なので、両方の条件が抽出されます。
         - 処理フロー③：それぞれのタイプと掲載開始日により注文件数を`紙：59, デジタル：0` 件算出
         - 処理フロー④⑤：注文件数と予算上限を比較：紙は `59 < 60`、デジタルは `0 < 999999` 且つ 優先順位`priority`紙のほうが高いので、掲載タイプは「**紙**」と判断します。

      - **ケースB**：
         条件:
         - `CURRENT_TIMESTAMP`: `2025-05-25 10:00:00`
         - `RANDSEL_ORDERS` の注文件数:
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=1` (紙)の注文件数: : **60**件
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=2` (デジタル)の注文件数: 0件

         処理:
         - 処理フロー②：紙とデジタルの両方が有効期間内なので、両方の条件が抽出されます。
         - 処理フロー③：それぞれのタイプと掲載開始日により注文件数を`紙：60, デジタル：0` 件算出
         - 処理フロー④⑤：注文件数と予算上限を比較：紙は `60 = 60`、デジタルは `0 < 999999`、デジタルの注文件数は予算上限より小さいため、掲載タイプは「**デジタル**」と判断します。


   - パターン２（同じタイプの条件が重複予算期間あり）：

      | カタログタイプ | type | budget_quantity | is_active | start_date | end_date | priority |
      |------------|------|----------------|-----------|------------|----------|----------|
      | 紙カタログ用１ | 1（紙） | 60 | true（有効） | 2025-05-01 | 2025-05-31 | 2 |
      | 紙カタログ用２ | 1（紙） | 80 | true（有効） | 2025-05-20 | 2025-06-30 | 2 |

      - **ケースA**：
         条件:
         - `CURRENT_TIMESTAMP`: `2025-05-25 10:00:00` (両方の条件が有効な期間)
         - `RANDSEL_ORDERS` の注文件数:
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=1` (紙)の注文件数: **60**件

         処理:
         - 処理フロー②：`type=1（紙）` で現在時刻の条件が抽出されます。同じタイプなので、二つの条件を合併し、予算上限`60 + 80 = 140` 件で、予算開始日を`min(start_date) = 2025-05-01` にします。
         - 処理フロー③：上記の条件により注文件数を`紙：60` 件算出
         - 処理フロー④⑤：注文件数と予算上限を比較：紙は `60 < 140`、紙の注文件数は予算上限より小さいため、掲載タイプは「**紙**」と判断します。

      - **ケースB**：
         条件:
         - `CURRENT_TIMESTAMP`: `2025-05-25 10:00:00` (両方の条件が有効な期間)
         - `RANDSEL_ORDERS` の注文件数:
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=1` (紙)の注文件数: **140**件

         処理:
         - 処理フロー②：`type=1（紙）` で現在時刻の条件が抽出されます。同じタイプなので、二つの条件を合併し、予算上限`60 + 80 = 140` 件で、予算開始日を`min(start_date) = 2025-05-01` にします。
         - 処理フロー③：上記の条件により注文件数を`紙：140` 件算出
         - 処理フロー④⑤：注文件数と予算上限を比較：紙は `140 = 140`、紙の注文件数は予算上限に等しい、且つ 現在時刻に他の予算が存在しないため、掲載タイプは「**売り切れ**」と判断します。


   - パターン３（予算期間途中で紙カタログが追加される）：

      | カタログタイプ | type | budget_quantity | is_active | start_date | end_date | priority |
      |------------|------|----------------|-----------|------------|----------|----------|
      | デジタルカタログ用 | 2（デジタル） | 999999（無限） | true（有効） | 2025-05-01 | 2025-05-31 | 1 |
      | 紙カタログ用２ | 1（紙） | 80 | true（有効） | 2025-05-20 | 2025-06-30 | 2 |

      - **ケースA**：
         条件:
         - `CURRENT_TIMESTAMP`: `2025-05-25 10:00:00`
         - `RANDSEL_ORDERS` の注文件数:
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=1` (紙)の注文件数: 0件
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=2` (デジタル)の注文件数: **1500**件

         処理:
         - 処理フロー②：紙とデジタルの両方が有効期間内なので、両方の条件が抽出されます。
         - 処理フロー③：それぞれのタイプと掲載開始日により注文件数を`紙：0, デジタル：1500` 件算出
         - 処理フロー④⑤：注文件数と予算上限を比較：紙は `0 < 80`、デジタルは `0 < 999999` 且つ 優先順位`priority`紙のほうが高いので、掲載タイプは「**紙**」と判断します。

      - **ケースB**：
         条件:
         - `CURRENT_TIMESTAMP`: `2025-05-25 10:00:00`
         - `RANDSEL_ORDERS` の注文件数:
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=1` (紙)の注文件数: **80**件
            - 5/1～現在時刻(CURRENT_TIMESTAMP) `type=2` (デジタル)の注文件数: **1500**件

         処理:
         - 処理フロー②：紙とデジタルの両方が有効期間内なので、両方の条件が抽出されます。
         - 処理フロー③：それぞれのタイプと掲載開始日により注文件数を`紙：80, デジタル：1500` 件算出
         - 処理フロー④⑤：注文件数と予算上限を比較：紙は `80 = 80`、デジタルは `0 < 999999`、デジタルの注文件数は予算上限より小さいため、掲載タイプは「**デジタル**」と判断します。

5. **掲載終了**:
   - 管理者による**手動**で表示状態を変更
   - ウェブサイト上で**カタログが非表示**になる
   - `PRODUCTS` テーブルの `is_display` を「`false`」に更新

6. **申し込み時の売り切れ判定**
   - 制御タイミング：本登録処理時と追加カタログ申し込み時
   - 対象カタログ：ユーザーが申し込んだカタログ
   - 売り切れ判定は`申し込みフォーム掲載タイプ（紙、PDF、売り切れ）の表示制御`を使用して、売り切れ判断後、ユーザーにメールで通知を行う

### 掲載条件の変更・調整

1. **予算数量の調整**:
   - メーカーからの依頼や運営判断により予算数量を変更
   - `BUDGETS` テーブルの `budget_quantity` を更新
   - 申し込みフォーム表示するタイミングで、掲載タイプの表示制御により掲載タイプが変わる

2. **掲載期間の延長**:
   - メーカーからの依頼や運営判断により掲載期間を延長
   - `BUDGETS` テーブルの `end_date` を更新
   - 申し込みフォーム表示するタイミングで、掲載タイプの表示制御により掲載タイプが変わる

3. **緊急停止**:
   - 商品情報の誤りや在庫切れなどの緊急事態発生時
   - 管理者が `PRODUCTS` テーブルの `dis_display` を「`false`」に手動更新
   - 必要に応じて `BUDGETS` テーブルの情報も更新

---
## 権限管理

### 1. ユーザー登録・役割設定
*   **一般ユーザー**: 新規登録時は `GENERAL_USERS` テーブルにレコードを作成。
*   **管理者ユーザー/管理者ユーザー（確定権限）**: 新規登録時は `SWB_USERS` テーブルにレコードを作成し、`authority_id`に権限を付ける。（100:admin, 101:admin_confirmer ）
*   **メーカーユーザー**: 新規登録時は `MAKER_USERS` テーブルにレコードを作成し、`maker_id` で `MAKERS` と紐付ける。

### 3. 権限チェック
ログインユーザーがどのログイン画面（一般ユーザーログイン画面、メーカーログイン画面、SWBログイン画面）から役割を判定し、アクセスできる機能を制御します。

* **一般ユーザーの場合（GENERAL_USERS）**
  * `GENERAL_USERS` テーブルで認証。
  * `USER_PROFILES` から基本データ、`RANDSEL_ORDERS` から注文データを取得し、一般ユーザー画面にアクセス可能。
  * `USER_TOKENS` でトークン管理。

* **管理者ユーザーの場合（SWB_USERS）**
  * `SWB_USERS` テーブルで認証し、`authority_id` で権限レベル（admin, admin_confirmer等）を判定。
  * SWB管理画面や管理者専用機能にアクセス可能。
  * `SWB_USER_TOKENS` でトークン管理。

* **メーカーユーザーの場合**
  * `MAKER_USERS` テーブルで認証し、`maker_id` を取得し、メーカー管理画面にアクセス可能。
  * `MAKER_USER_TOKENS` でトークン管理。
---

## 過去のkuroco保管しているユーザーの扱い

### kurocoユーザーをDBへ移行するバッチ
1. kurocoユーザーの個人情報、アンケート情報を`GENERAL_USERS`、`USER_PROFILES`、`USER_SURVEYS` テーブルに登録
2. `GENERAL_USERS.password` を`null`にします。

### kurcoユーザーログイン流れ
1. ログインページに入力された`email` を用いて、`GENERAL_USERS`テーブルに登録しているかを判断
2. `GENERAL_USERS`テーブルに登録していると判断した場合、`GENERAL_USERS.password` を取り出す
3. `GENERAL_USERS.password == null` の場合、kurcoユーザーと判断し、kurcoのログインエンドポイントにアクセス