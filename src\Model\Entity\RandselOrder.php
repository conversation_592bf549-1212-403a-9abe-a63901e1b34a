<?php
declare(strict_types=1);

namespace App\Model\Entity;

use App\Model\Entity\EntityTrait\RandselOrderTrait;
use Cake\I18n\FrozenTime;
use Cake\ORM\Entity;

/**
 * RandselOrder Entity
 *
 * @property int $id
 * @property int $maker_id
 * @property int|null $member_id
 * @property int|null $general_user_id
 * @property int $product_id
 * @property string $product_name
 * @property int $price
 * @property int|null $status
 * @property FrozenTime|null $status_modified
 * @property int|null $approval_type
 * @property bool|null $is_confirmed
 * @property FrozenTime|null $confirmed
 * @property string $name1
 * @property string $name2
 * @property string $name1_hurigana
 * @property string $name2_hurigana
 * @property string $zip_code
 * @property string $tdfk_cd
 * @property string $address1
 * @property string $address2
 * @property string|null $address3
 * @property string $tel
 * @property string $email
 * @property bool|null $email_send_ng_flg
 * @property string $survey_json
 * @property FrozenTime|null $created
 * @property FrozenTime|null $modified
 * 
 * @property \App\Model\Entity\GeneralUser|null $general_user
 */
class RandselOrder extends Entity
{
    use RandselOrderTrait;

    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected $_accessible = [
        'maker_id' => true,
        'member_id' => true,
        'general_user_id' => true,
        'product_id' => true,
        'product_name' => true,
        'price' => true,
        'status' => true,
        'status_modified' => true,
        'approval_type' => true,
        'is_confirmed' => true,
        'confirmed' => true,
        'name1' => true,
        'name2' => true,
        'name1_hurigana' => true,
        'name2_hurigana' => true,
        'zip_code' => true,
        'tdfk_cd' => true,
        'address1' => true,
        'address2' => true,
        'address3' => true,
        'tel' => true,
        'email' => true,
        'email_send_ng_flg' => true,
        'survey_json' => true,
        'created' => true,
        'modified' => true,
    ];
}
