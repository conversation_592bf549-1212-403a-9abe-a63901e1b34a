<?php

namespace App\Authentication\Authenticator;

use App\Authentication\UserAuthenticationService;
use App\Enums\EntityFields\ELogin;
use App\Kuroko\Entity\Member;
use Authentication\Authenticator\AbstractAuthenticator;
use Authentication\Authenticator\Result;
use Authentication\Authenticator\ResultInterface;
use Authentication\Authenticator\StatelessInterface;
use Cake\Log\Log;
use Psr\Http\Message\ServerRequestInterface;

class UserAuthenticator extends AbstractAuthenticator implements StatelessInterface
{
    /**
     * @inheritdoc
     */
    protected $_defaultConfig = [
        'header' => 'Authorization-coverme',
        'tokenPrefix' => 'Bearer',
        'fields' => [
            ELogin::LOGIN_EMAIL,
            ELogin::LOGIN_PWD,
        ],
    ];


    /**
     * 認証処理
     * @param ServerRequestInterface $request
     * @return ResultInterface
     */
    public function authenticate(ServerRequestInterface $request): ResultInterface
    {
        Log::debug("UserAuthenticator::authenticate called");
        $conditions = $this->_getData($request);
        $conditions[ELogin::TOKEN->value] = $this->getToken($request);
        /** @var Member $user */
        $user = $this->_identifier->identify($conditions);
        Log::debug("UserAuthenticator: User found: " . json_encode($user));
        if (empty($user)) {
            return new Result(null, ResultInterface::FAILURE_IDENTITY_NOT_FOUND, $this->_identifier->getErrors());
        }

        // $isClientKeyFlagSet = $this->getConfig(UserAuthenticationService::CLIENT_KEY_FLAG);
        // $isUserInClientGroup = $user->isBelongsToClientGroup();

        $isCheckStatusFlagSet = $this->getConfig(UserAuthenticationService::CHECK_STATUS_FLAG);
        $isUserStatusAuthenticated = $user->isStatusAuthenticated();

        // $isSwbKeyFlagSet = $this->getConfig(UserAuthenticationService::SWB_KEY_FLAG);
        // $isUserInSwbGroup = $user->isBelongsToSwbGroup();

        foreach ($this->getConfig(UserAuthenticationService::ALLOWED_GROUP) as $allowedGroupId) {
            if (!in_array($allowedGroupId, $user->getGroupIds())) {
                Log::error("認証スコープ1".json_encode($user->getGroupIds()));
                return new Result(null, ResultInterface::FAILURE_IDENTITY_NOT_FOUND, $this->_identifier->getErrors());
            }
        }

        if ($isCheckStatusFlagSet != $isUserStatusAuthenticated) {
            Log::error("認証スコープ2".json_encode($user->getGroupIds()));
            Log::error("isCheckStatusFlagSet: " . ($isCheckStatusFlagSet ? 'true' : 'false'));
            Log::error("isUserStatusAuthenticated: " . ($isUserStatusAuthenticated ? 'true' : 'false'));
            return new Result(null, ResultInterface::FAILURE_IDENTITY_NOT_FOUND, $this->_identifier->getErrors());
        }

        return new Result($user, ResultInterface::SUCCESS);

        // if (
        //     // SWBアカウントの場合
        //     ($isSwbKeyFlagSet && $isUserInSwbGroup) ||
        //     // メーカーアカウントの場合
        //     ($isClientKeyFlagSet && $isUserInClientGroup) ||
        //     // ユーザーアカウントの場合
        //     (!$isClientKeyFlagSet && !$isUserInClientGroup &&
        //         // 認証済み限定コントローラー
        //         (($isCheckStatusFlagSet && $isUserStatusAuthenticated) ||
        //             // 未認証限定コントローラー
        //             (!$isCheckStatusFlagSet && !$isUserStatusAuthenticated))
        //     )
        // ) {
        //     return new Result($user, ResultInterface::SUCCESS);
        // }
        // //
        // Log::error("認証スコープ");
        // return new Result(null, ResultInterface::FAILURE_IDENTITY_NOT_FOUND, $this->_identifier->getErrors());
    }

    public function unauthorizedChallenge(ServerRequestInterface $request): void {}


    /**
     * Checks if the token is in the headers or a request parameter
     *
     * @param ServerRequestInterface $request The request that contains login information.
     * @return string|null
     */
    protected function getToken(ServerRequestInterface $request): ?string
    {
        $token = $this->getTokenFromHeader($request, $this->getConfig('header'));
        $prefix = $this->getConfig('tokenPrefix');
        if ($prefix !== null && is_string($token)) {
            return $this->stripTokenPrefix($token, $prefix);
        }
        return $token;
    }

    /**
     * Strips a prefix from a token
     *
     * @param string $token Token string
     * @param string $prefix Prefix to strip
     * @return string
     */
    protected function stripTokenPrefix(string $token, string $prefix): string
    {
        return str_ireplace($prefix . ' ', '', $token);
    }

    /**
     * Gets the token from the request headers
     *
     * @param ServerRequestInterface $request The request that contains login information.
     * @param string|null $headerLine Header name
     * @return string|null
     */
    protected function getTokenFromHeader(ServerRequestInterface $request, ?string $headerLine): ?string
    {
        if (!empty($headerLine)) {
            $header = $request->getHeaderLine($headerLine);
            if (!empty($header)) {
                return $header;
            }
        }

        return null;
    }

    /**
     * Checks the fields to ensure they are supplied.
     *
     * @param ServerRequestInterface $request The request that contains login information.
     * @return array|null Username and password retrieved from a request body.
     */
    protected function _getData(ServerRequestInterface $request): ?array
    {
        /** @var ELogin[] $fields */
        $fields = $this->_config['fields'];
        /** @var array $body */
        $body = $request->getParsedBody();
        $data = [];
        foreach ($fields as $key => $eField) {
            $field = $eField->value;
            if (!isset($body[$field])) {
                continue;
            }

            $value = $body[$field];
            if (!is_string($value) || !strlen($value)) {
                continue;
            }
            $data[$field] = $value;
        }

        return $data;
    }
}
