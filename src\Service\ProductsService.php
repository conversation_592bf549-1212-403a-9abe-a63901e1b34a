<?php

namespace App\Service;

use App\Kuroko\ApiModel\KurokoApiPrivateStatic\Products;
use BadMethodCallException;
use Cake\Datasource\EntityInterface;

class ProductsService implements IRestService
{
    use ServiceTrait;

    /**
     * @var array
     */
    protected array $_defaultConfig = [
    ];

    public function add(array $data = []): ?EntityInterface
    {
        throw new BadMethodCallException();
    }

    public function edit(string $id, array $data = []): ?EntityInterface
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): ?EntityInterface
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): ?array
    {
        $products = new Products();
        return $products->getProductList();
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    public function initialize(): void
    {
    }
}
