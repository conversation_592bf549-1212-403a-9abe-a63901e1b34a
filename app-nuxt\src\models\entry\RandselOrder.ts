import {
    PREFECTURE_LIST,
    RANDSEL_APPROVAL_STATUS_LIST,
    getList<PERSON>abelByValue,
} from "~~/src/list";
import moment from "moment";

export default class RandselOrder {
    private readonly _id: number;
    private readonly _maker_id: number;
    private readonly _member_id: number;
    private readonly _product_id: number;
    private readonly _product_name: string;
    private readonly _price: number;
    private _status: number;
    private readonly _status_modified: string | null;
    private readonly _approval_type: string | null;
    private readonly _is_confirmed: boolean;
    private readonly _confirmed: string | null;
    private readonly _name1: string;
    private readonly _name2: string;
    private readonly _name1_hurigana: string;
    private readonly _name2_hurigana: string;
    private readonly _zip_code: string;
    private readonly _tdfk_cd: string;
    private readonly _address1: string;
    private readonly _address2: string;
    private readonly _address3: string;
    private readonly _tel: string;
    private readonly _email: string;
    private readonly _email_send_ng_flg: boolean;
    private readonly _survey_json: string;
    private readonly _created: string;
    private readonly _modified: string;

    constructor(data: TRandselOrder) {
        this._id = data.id;
        this._maker_id = data.maker_id;
        this._member_id = data.member_id;
        this._product_id = data.product_id;
        this._product_name = data.product_name;
        this._price = data.price;
        this._status = data.status;
        this._status_modified = data.status_modified;
        this._approval_type = data.approval_type;
        this._is_confirmed = data.is_confirmed;
        this._confirmed = data.confirmed;
        this._name1 = data.name1;
        this._name2 = data.name2;
        this._name1_hurigana = data.name1_hurigana;
        this._name2_hurigana = data.name2_hurigana;
        this._zip_code = data.zip_code;
        this._tdfk_cd = data.tdfk_cd;
        this._address1 = data.address1;
        this._address2 = data.address2;
        this._address3 = data.address3;
        this._tel = data.tel;
        this._email = data.email;
        this._email_send_ng_flg = data.email_send_ng_flg;
        this._survey_json = data.survey_json;
        this._created = data.created;
        this._modified = data.modified;
    }

    get id(): number {
        return this._id;
    }

    get maker_id(): number {
        return this._maker_id;
    }

    get member_id(): number {
        return this._member_id;
    }

    get product_id(): number {
        return this._product_id;
    }

    get product_name(): string {
        return this._product_name;
    }

    get price(): number {
        return this._price;
    }

    get status(): number {
        return this._status;
    }

    get display_status(): string {
        return getListLabelByValue(this._status, RANDSEL_APPROVAL_STATUS_LIST);
    }

    get status_modified(): string | null {
        return this._status_modified;
    }

    get approval_type(): string | null {
        return this._approval_type;
    }

    get is_confirmed(): boolean {
        return this._is_confirmed;
    }

    get confirmed(): string | null {
        return this._confirmed;
    }

    get name1(): string {
        return this._name1;
    }

    get name2(): string {
        return this._name2;
    }

    get name(): string {
        return `${this._name1} ${this._name2}`;
    }

    get name1_hurigana(): string {
        return this._name1_hurigana;
    }

    get name2_hurigana(): string {
        return this._name2_hurigana;
    }

    get zip_code(): string {
        return this._zip_code;
    }

    get tdfk_cd(): string {
        return this._tdfk_cd;
    }

    get display_tdfk_cd(): string {
        return getListLabelByValue(this._tdfk_cd, PREFECTURE_LIST);
    }

    get address1(): string {
        return this._address1;
    }

    get address2(): string {
        return this._address2;
    }

    get address3(): string {
        return this._address3;
    }

    get tel(): string {
        return this._tel;
    }

    get email(): string {
        return this._email;
    }

    get email_send_ng_flg(): boolean {
        return this._email_send_ng_flg;
    }

    get survey_json(): string {
        return this._survey_json;
    }

    get created(): string {
        return this._created;
    }

    get display_created(): string {
        return moment(this._created).format("YYYY-MM-DD HH:mm");
    }

    get modified(): string {
        return this._modified;
    }

    static creates(orders: TRandselOrder[]): RandselOrder[] {
        return orders.map((order: TRandselOrder) => RandselOrder.create(order));
    }

    static create(order: TRandselOrder): RandselOrder {
        return new RandselOrder(order);
    }
}
