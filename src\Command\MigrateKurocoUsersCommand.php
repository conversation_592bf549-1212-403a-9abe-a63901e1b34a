<?php

declare(strict_types=1);

namespace App\Command;

use App\Enums\EntityFields\EUserProfile;
use App\Kuroko\ApiModel\KurokoApiDynamic\Members;
use App\Kuroko\Entity\Member;
use App\Model\Table\GeneralUsersTable;
use App\Model\Table\UserProfilesTable;

use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\Datasource\ConnectionManager;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Exception;

/**
 * Kurocoから新しいMySQLベースのユーザー管理システムへの一般ユーザーデータ移行バッチ
 * 
 * 目的：
 * - Kuroco APIから取得した一般ユーザーの個人情報を新しい認証システムに移行
 * - general_usersテーブルとuser_profilesテーブルに適切にデータを分散
 * - 暗号化が必要な個人情報フィールドを適切に処理
 */
class MigrateKurocoUsersCommand extends Command
{
    private GeneralUsersTable $generalUsersTable;
    private UserProfilesTable $userProfilesTable;
    private Members $membersApi;
    
    private int $successCount = 0;
    private int $errorCount = 0;
    private int $skippedCount = 0;
    private array $errors = [];

    /**
     * コマンドオプションの定義
     */
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser
            ->setDescription([
                'Kurocoから新しいMySQLベースのユーザー管理システムへの一般ユーザーデータ移行バッチ',
                '',
                'Kuroco APIから取得した一般ユーザーの個人情報を、新しい認証システムの',
                'general_usersテーブルとuser_profilesテーブルに移行します。',
                '',
                '注意事項：',
                '- 既存の認証システム（Kuroco）との互換性を維持',
                '- 暗号化が必要な個人情報フィールドは適切に処理',
                '- password=nullでKurocoユーザーを識別',
            ])
            ->addOption('dry-run', [
                'help' => 'ドライランモード（実際の登録は行わない、処理内容のみ表示）',
                'boolean' => true,
                'default' => false,
            ])
            ->addOption('batch-size', [
                'help' => 'バッチサイズ（一度に処理するユーザー数）',
                'short' => 'b',
                'default' => 100,
            ])
            ->addOption('user-ids', [
                'help' => '処理対象のユーザーID（カンマ区切り）。指定しない場合は全ユーザーを処理',
                'short' => 'u',
                'default' => null,
            ])
            ->addOption('verbose', [
                'help' => '詳細な実行ログを出力',
                'short' => 'v',
                'boolean' => true,
                'default' => false,
            ])
            ->addOption('skip-existing', [
                'help' => '既存データをスキップ（重複チェック）',
                'boolean' => true,
                'default' => true,
            ]);

        return $parser;
    }

    /**
     * コマンド実行
     */
    public function execute(Arguments $args, ConsoleIo $io): ?int
    {
        $dryRun = $args->getOption('dry-run');
        $batchSize = (int)$args->getOption('batch-size');
        $userIds = $args->getOption('user-ids');
        $verbose = $args->getOption('verbose');
        $skipExisting = $args->getOption('skip-existing');

        // 初期化
        $this->initializeCommand();
        
        $io->out('<info>Kurocoユーザーデータ移行バッチを開始します...</info>');

        if ($dryRun) {
            $io->out('ドライランモードで実行します（実際の登録は行いません）');
        }

        $io->out("バッチサイズ: {$batchSize}");
        $io->out("既存データスキップ: " . ($skipExisting ? 'ON' : 'OFF'));

        try {
            // 処理対象ユーザーIDの取得
            $targetUserIds = $this->getTargetUserIds($userIds, $io, $verbose);
            
            if (empty($targetUserIds)) {
                $io->warning('処理対象のユーザーが見つかりませんでした。');
                return static::CODE_SUCCESS;
            }

            $totalUsers = count($targetUserIds);
            $io->out("処理対象ユーザー数: {$totalUsers}");

            // バッチ処理実行
            $this->processBatches($targetUserIds, $batchSize, $dryRun, $skipExisting, $io, $verbose);

            // 結果表示
            $this->displayResults($io);

            return static::CODE_SUCCESS;

        } catch (Exception $e) {
            $io->error("移行処理中にエラーが発生しました: " . $e->getMessage());
            Log::error("MigrateKurocoUsersCommand failed: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return static::CODE_ERROR;
        }
    }

    /**
     * 初期化処理
     */
    private function initializeCommand(): void
    {
        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
        $this->membersApi = new Members();
        
        $this->successCount = 0;
        $this->errorCount = 0;
        $this->skippedCount = 0;
        $this->errors = [];
    }

    /**
     * 処理対象ユーザーIDの取得
     */
    private function getTargetUserIds(?string $userIds, ConsoleIo $io, bool $verbose): array
    {
        if ($userIds) {
            // 指定されたユーザーIDを処理
            $targetIds = array_map('intval', explode(',', $userIds));
            if ($verbose) {
                $io->out("指定されたユーザーID: " . implode(', ', $targetIds));
            }
            return $targetIds;
        }

        // 全ユーザーを対象とする場合の実装
        // 実際の運用では、適切な条件でユーザーIDを取得する必要があります
        if ($verbose) {
            $io->out('全ユーザーを処理対象とします（実装時に適切な条件を設定してください）');
        }
        
        // 例: 1から1000までのIDを対象とする（実際の実装では適切な条件に変更）
        return range(1, 10000);
    }

    /**
     * バッチ処理実行
     */
    private function processBatches(
        array $targetUserIds, 
        int $batchSize, 
        bool $dryRun, 
        bool $skipExisting, 
        ConsoleIo $io, 
        bool $verbose
    ): void {
        $batches = array_chunk($targetUserIds, $batchSize);
        $totalBatches = count($batches);

        foreach ($batches as $batchIndex => $batchUserIds) {
            $currentBatch = $batchIndex + 1;
            $io->out("バッチ {$currentBatch}/{$totalBatches} を処理中...");

            try {
                // Kuroco APIからユーザーデータを取得
                $members = $this->membersApi->listForMakerByIds($batchUserIds);
                
                if ($verbose) {
                    $io->out("  取得したユーザー数: " . count($members));
                }

                // 各ユーザーを処理
                foreach ($members as $member) {
                    $this->processUser($member, $dryRun, $skipExisting, $io, $verbose);
                }

            } catch (Exception $e) {
                $this->errorCount += count($batchUserIds);
                $error = "バッチ {$currentBatch} の処理中にエラー: " . $e->getMessage();
                $this->errors[] = $error;
                $io->error($error);
                Log::error($error, ['batch_user_ids' => $batchUserIds]);
            }
        }
    }

    /**
     * 個別ユーザーの処理
     */
    private function processUser(Member $member, bool $dryRun, bool $skipExisting, ConsoleIo $io, bool $verbose): void
    {
        try {
            $memberId = $member->getId();
            $email = $member->getEmail();

            if (empty($email)) {
                $this->skippedCount++;
                if ($verbose) {
                    $io->out("  ユーザーID {$memberId}: メールアドレスが空のためスキップ");
                }
                return;
            }

            // 既存データチェック
            if ($skipExisting && $this->isUserExists($memberId, $email)) {
                $this->skippedCount++;
                if ($verbose) {
                    $io->out("  ユーザーID {$memberId}: 既存データのためスキップ");
                }
                return;
            }

            if ($dryRun) {
                $this->displayDryRunInfo($member, $io, $verbose);
                $this->successCount++;
                return;
            }

            // 実際のデータ移行処理
            $this->migrateUser($member, $io, $verbose);
            $this->successCount++;

            if ($verbose) {
                $io->out("  ユーザーID {$memberId}: 移行完了");
            }

        } catch (Exception $e) {
            $this->errorCount++;
            $error = "ユーザーID {$member->getId()} の処理中にエラー: " . $e->getMessage();
            $this->errors[] = $error;
            $io->error("  " . $error);
            Log::error($error, ['member_data' => $member->getData()]);
        }
    }

    /**
     * 既存ユーザーの存在チェック
     */
    private function isUserExists(string $memberId, string $email): bool
    {
        // IDまたはメールアドレスで既存ユーザーをチェック
        $existingUser = $this->generalUsersTable->find()
            ->where([
                'OR' => [
                    'id' => $memberId,
                    'email' => $email
                ]
            ])
            ->first();

        return $existingUser !== null;
    }

    /**
     * ドライラン情報の表示
     */
    private function displayDryRunInfo(Member $member, ConsoleIo $io, bool $verbose): void
    {
        if (!$verbose) {
            return;
        }

        $memberId = $member->getId();
        $email = $member->getEmail();

        $io->out("  ユーザーID {$memberId} ({$email}):");
        $io->out("    general_users: id={$memberId}, email={$email}, password=null");

        $profileData = $this->mapMemberToProfileData($member);
        $io->out("    user_profiles: " . json_encode($profileData, JSON_UNESCAPED_UNICODE));
    }

    /**
     * ユーザーデータの実際の移行処理
     */
    private function migrateUser(Member $member, ConsoleIo $io, bool $verbose): void
    {
        $connection = ConnectionManager::get('default');

        $connection->transactional(function () use ($member, $io, $verbose) {
            // general_usersテーブルへの登録
            $generalUser = $this->createOrUpdateGeneralUser($member);

            // user_profilesテーブルへの登録
            $this->createOrUpdateUserProfile($generalUser, $member);

            if ($verbose) {
                Log::info("User migration completed", [
                    'member_id' => $member->getId(),
                    'email' => $member->getEmail()
                ]);
            }
        });
    }

    /**
     * general_usersテーブルへのユーザー作成/更新
     */
    private function createOrUpdateGeneralUser(Member $member): \App\Model\Entity\GeneralUser
    {
        $memberId = $member->getId();
        $email = $member->getEmail();

        // 既存ユーザーの検索
        $existingUser = $this->generalUsersTable->find()
            ->where(['id' => $memberId])
            ->first();

        if ($existingUser) {
            // 既存ユーザーの更新
            $existingUser = $this->generalUsersTable->patchEntity($existingUser, [
                'email' => $email,
                'password' => null, // Kurocoユーザーの識別用
                'modified' => $this->parseKurocoDateTime($member->get('update_ymdhi'))
            ]);

            $user = $this->generalUsersTable->save($existingUser);
        } else {
            // 新規ユーザーの作成
            $userData = [
                'email' => $email,
                'password' => null, // Kurocoユーザーの識別用
                'created' => $this->parseKurocoDateTime($member->get('inst_ymdhi')),
                'modified' => $this->parseKurocoDateTime($member->get('update_ymdhi'))
            ];

            $user = $this->generalUsersTable->newEntity($userData);

            // 手動でIDを設定（Kurocoのmember_idを使用）
            $user->id = $memberId;

            $user = $this->generalUsersTable->save($user);
        }

        if (!$user) {
            throw new Exception("Failed to save general user: " . json_encode($this->generalUsersTable->getErrors()));
        }

        return $user;
    }

    /**
     * user_profilesテーブルへのプロフィール作成/更新
     */
    private function createOrUpdateUserProfile(\App\Model\Entity\GeneralUser $generalUser, Member $member): void
    {
        // 既存プロフィールの検索
        $existingProfile = $this->userProfilesTable->find()
            ->where(['general_user_id' => $generalUser->id])
            ->first();

        $profileData = $this->mapMemberToProfileData($member);
        $profileData['general_user_id'] = $generalUser->id;
        $profileData['created'] = $this->parseKurocoDateTime($member->get('inst_ymdhi'));
        $profileData['modified'] = $this->parseKurocoDateTime($member->get('update_ymdhi'));

        if ($existingProfile) {
            // 既存プロフィールの更新
            $profile = $this->userProfilesTable->patchEntity($existingProfile, $profileData);
        } else {
            // 新規プロフィールの作成
            $profile = $this->userProfilesTable->newEntity($profileData);
        }

        $profile = $this->userProfilesTable->save($profile);

        if (!$profile) {
            throw new Exception("Failed to save user profile: " . json_encode($this->userProfilesTable->getErrors()));
        }
    }

    /**
     * MemberデータをUserProfileフィールドにマッピング
     * UserDetailsService::mapDataToProfileFieldsを参考に実装
     */
    private function mapMemberToProfileData(Member $member): array
    {
        $memberData = $member->getData();
        $profileData = [];

        // フィールドマッピング（UserDetailsServiceと同じマッピング）
        $fieldMapping = [
            'name1' => EUserProfile::LAST_NAME->value,
            'name2' => EUserProfile::FIRST_NAME->value,
            'name1_hurigana' => EUserProfile::LAST_NAME_KANA->value,
            'name2_hurigana' => EUserProfile::FIRST_NAME_KANA->value,
            'zip_code' => EUserProfile::ZIP_CODE->value,
            'tdfk_cd' => EUserProfile::PREFECTURE_CODE->value,
            'address1' => EUserProfile::ADDRESS1->value,
            'address2' => EUserProfile::ADDRESS2->value,
            'address3' => EUserProfile::ADDRESS3->value,
            'tel' => EUserProfile::TEL->value,
            'email_send_ng_flg' => EUserProfile::EMAIL_SEND_NG_FLG->value,
        ];

        foreach ($fieldMapping as $memberKey => $profileField) {
            $value = Hash::get($memberData, $memberKey);
            if ($value !== null && $value !== '') {
                $profileData[$profileField] = $value;
            }
        }

        // デフォルト値の設定
        $this->setDefaultProfileValues($profileData);

        return $profileData;
    }

    /**
     * プロフィールデータのデフォルト値設定
     */
    private function setDefaultProfileValues(array &$profileData): void
    {
        // 必須フィールドのデフォルト値設定
        $requiredFields = [
            EUserProfile::LAST_NAME->value => '',
            EUserProfile::FIRST_NAME->value => '',
            EUserProfile::LAST_NAME_KANA->value => '',
            EUserProfile::FIRST_NAME_KANA->value => '',
            EUserProfile::ZIP_CODE->value => '',
            EUserProfile::PREFECTURE_CODE->value => '',
            EUserProfile::ADDRESS1->value => '',
            EUserProfile::TEL->value => '',
            EUserProfile::EMAIL_SEND_NG_FLG->value => false,
        ];

        foreach ($requiredFields as $field => $defaultValue) {
            if (!isset($profileData[$field]) || $profileData[$field] === null) {
                $profileData[$field] = $defaultValue;
            }
        }

        // nullable フィールドの処理
        $nullableFields = [
            EUserProfile::ADDRESS2->value,
            EUserProfile::ADDRESS3->value,
            EUserProfile::NOTES->value,
        ];

        foreach ($nullableFields as $field) {
            if (!isset($profileData[$field])) {
                $profileData[$field] = null;
            }
        }
    }

    /**
     * Kuroco日時文字列をFrozenTimeに変換
     */
    private function parseKurocoDateTime(?string $dateTimeString): ?FrozenTime
    {
        if (empty($dateTimeString)) {
            return null;
        }

        try {
            return new FrozenTime($dateTimeString);
        } catch (Exception $e) {
            Log::warning("Failed to parse Kuroco datetime: {$dateTimeString}", [
                'error' => $e->getMessage()
            ]);
            return new FrozenTime(); // 現在時刻をデフォルトとする
        }
    }

    /**
     * 実行結果の表示
     */
    private function displayResults(ConsoleIo $io): void
    {
        $io->out('');
        $io->out('<info>=== 移行結果 ===</info>');
        $io->out("成功: {$this->successCount} 件");
        $io->out("スキップ: {$this->skippedCount} 件");
        $io->out("エラー: {$this->errorCount} 件");

        if (!empty($this->errors)) {
            $io->out('');
            $io->out('<error>=== エラー詳細 ===</error>');
            foreach ($this->errors as $error) {
                $io->error($error);
            }
        }

        $io->out('');
        $io->success('Kurocoユーザーデータ移行バッチが完了しました。');

        // ログ出力
        Log::info('MigrateKurocoUsersCommand completed', [
            'success_count' => $this->successCount,
            'skipped_count' => $this->skippedCount,
            'error_count' => $this->errorCount,
            'total_errors' => count($this->errors)
        ]);
    }
}
