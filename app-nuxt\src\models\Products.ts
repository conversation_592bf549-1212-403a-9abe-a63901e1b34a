import { Model } from "~~/src/models/Model";
import { Product } from "~~/src/models/entry/Product";
import ProductsClient from "~/src/lib/http/coverme-build/ProductsClient";
import AllProductsClient from "~/src/lib/http/coverme-build/AllProductsClient";

export default class Products extends Model {
    public getList(
        params?: Record<string, string | number>,
    ): Promise<Product[]> {
        const config = this.config;
        return new Promise((resolve) => {
            // const kurokoEcProductClient = KurokoEcProductClient.create(config);
            ProductsClient.create(config)
                .index<TResponseProducts>(params)
                .then(({ products }) => {
                    resolve(Product.creates(products));
                })
                .catch((e) => console.error(e));
        });
    }

    public getAllList(
        params?: Record<string, string | number>,
    ): Promise<Product[]> {
        const config = this.config;
        return new Promise((resolve) => {
            // const kurokoEcProductClient = KurokoEcProductClient.create(config);
            AllProductsClient.create(config)
                .index<TResponseProducts>(params)
                .then(({ products }) => {
                    resolve(Product.creates(products));
                })
                .catch((e) => console.error(e));
        });
    }
}
