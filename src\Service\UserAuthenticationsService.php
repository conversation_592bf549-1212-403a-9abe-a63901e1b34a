<?php

namespace App\Service;

use App\Kuroko\ApiModel\KurokoApiDynamic\Logins;
use App\Kuroko\Entity\IKurokoEntity;
use App\Kuroko\Entity\Member;
use Authentication\Controller\Component\AuthenticationComponent;
use Authentication\Authenticator\UnauthenticatedException;
use BadMethodCallException;
use Cake\Datasource\EntityInterface;
use Cake\Core\Configure;
use Cake\Log\Log;

class UserAuthenticationsService implements IRestService
{
    use ServiceTrait;

    /**
     * @var array
     */
    protected array $_defaultConfig = [];

    private AuthenticationComponent $_authenticationComponent;


    public function initialize(): void
    {
    }

    public function getAuthenticationComponent(): AuthenticationComponent
    {
        return $this->_authenticationComponent;
    }

    public function setAuthenticationComponent(AuthenticationComponent $authenticationComponent): static
    {
        $this->_authenticationComponent = $authenticationComponent;
        return $this;
    }

    public function add(array $data = []): EntityInterface|null|IKurokoEntity
    {
        $result = $this->getAuthenticationComponent()->getResult();
        if (!$result->isValid()) {
            throw new UnauthenticatedException('Invalid credentials');
        }
        
        /** @var Member $member */
        $member = $result->getData();
        return $member;
    }

    public function edit(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): EntityInterface|null|IKurokoEntity
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): ?array
    {
        throw new BadMethodCallException();
    }

    public function delete(string $id, array $data = []): bool
    {
        /** @var Member $member */
        $member = $this->getIdentity();
        $accessToken = $member->getAccessToken();

        Log::info("UserAuthenticationsService: Starting logout process for member ID: " . $member->getId());

        // 新システムユーザーかKurocoユーザーかを判別してログアウト処理を実行
        if ($this->isNewSystemUser($member)) {
            Log::info("UserAuthenticationsService: Processing new system user logout");
            return $this->logoutNewSystemUser($member, $accessToken);
        } else {
            Log::info("UserAuthenticationsService: Processing Kuroco user logout");
            return $this->logoutKurocoUser($accessToken);
        }
    }

    /**
     * 新システムユーザーかどうかを判定
     */
    private function isNewSystemUser(Member $member): bool
    {
        return $member->get("details.is_new_system_user") ?? false;
    }

    /**
     * 新システムユーザーのログアウト処理
     */
    private function logoutNewSystemUser(Member $member, \App\Kuroko\Entity\AccessToken $accessToken): bool
    {
        try {
            Log::info("UserAuthenticationsService: Starting new system user logout");

            $token = \App\Kuroko\Entity\AccessToken::decryptToken($accessToken->encryptToken());
            if (!$token) {
                Log::error("UserAuthenticationsService: Failed to decrypt access token");
                return false;
            }

            $logoutSuccess = false;

            // 各ユーザータイプのトークンテーブルから該当トークンを削除
            $groupIds = $member->getGroupIds();
            $generalGroupId = Configure::read("Kuroko.api.userGroupId");
            $swbGroupId = Configure::read("Kuroko.api.swbGroupId");
            $clientGroupId = Configure::read("Kuroko.api.clientGroupId");

            // 一般ユーザーのトークン削除
            if (in_array($generalGroupId, $groupIds)) {
                $userTokensTable = $this->getTableLocator()->get('UserTokens');
                if ($userTokensTable->invalidateToken($token)) {
                    Log::info("UserAuthenticationsService: General user token invalidated successfully");
                    $logoutSuccess = true;
                }
            }

            // SWBユーザーのトークン削除
            if (in_array($swbGroupId, $groupIds)) {
                $swbTokensTable = $this->getTableLocator()->get('SwbUserTokens');
                if ($swbTokensTable->invalidateToken($token)) {
                    Log::info("UserAuthenticationsService: SWB user token invalidated successfully");
                    $logoutSuccess = true;
                }
            }

            // メーカーユーザーのトークン削除
            if (in_array($clientGroupId, $groupIds)) {
                $makerTokensTable = $this->getTableLocator()->get('MakerUserTokens');
                if ($makerTokensTable->invalidateToken($token)) {
                    Log::info("UserAuthenticationsService: Maker user token invalidated successfully");
                    $logoutSuccess = true;
                }
            }

            if ($logoutSuccess) {
                Log::info("UserAuthenticationsService: New system user logout completed successfully");
                return true;
            } else {
                Log::warning("UserAuthenticationsService: No tokens were invalidated for new system user");
                return false;
            }

        } catch (\Exception $e) {
            Log::error("UserAuthenticationsService: New system user logout error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Kurocoユーザーのログアウト処理
     */
    private function logoutKurocoUser(\App\Kuroko\Entity\AccessToken $accessToken): bool
    {
        try {
            Log::info("UserAuthenticationsService: Starting Kuroco user logout");

            $logins = new Logins();
            $result = $logins->logout($accessToken);

            if ($result) {
                Log::info("UserAuthenticationsService: Kuroco user logout completed successfully");
            } else {
                Log::warning("UserAuthenticationsService: Kuroco user logout failed");
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("UserAuthenticationsService: Kuroco user logout error: " . $e->getMessage());
            return false;
        }
    }
}
