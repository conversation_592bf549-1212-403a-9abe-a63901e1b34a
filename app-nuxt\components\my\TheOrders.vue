<script setup lang="ts">
import { useRuntimeConfig } from "#app";
import { onMounted, ref } from "vue";
import Orders from "~/src/models/Orders";
import type RandselOrder from "~/src/models/entry/RandselOrder";
import TheOrder from "~/components/my/TheOrder.vue";
import { useAuthStore } from "~/src/stores/auth";

const authStore = useAuthStore();
const config = useRuntimeConfig();
const orders = ref<RandselOrder[]>([]);
const isLoading = ref(true);

const headers = ref([
    { title: "商品名", value: "product_name" },
    { title: "備考", value: "" },
    // { title: "姓", value: "order_nm01" },
    // { title: "名", value: "order_nm02" },
    // { title: "操作", value: "actions" },
]);
onMounted(async () => {
    try {
        orders.value = await Orders.create(config).index();
    } catch (error) {
        console.error("Error fetching user details:", error);
        authStore.clearAuth();
    } finally {
        isLoading.value = false;
    }
});
const dialog = ref(false);
const selectedOrder = ref<RandselOrder | null>(null);
const openDialog = (order: RandselOrder): void => {
    selectedOrder.value = order;
    dialog.value = true;
};
const closeDialog = (): void => {
    selectedOrder.value = null;
    dialog.value = false;
};
</script>

<template>
    <v-container>
        <p class="text-h6 font-weight-bold mb-5">
            追加のカタログお申込は<NuxtLink to="/form/randsel/">
                こちら
            </NuxtLink>
        </p>
        <p class="text-h6 font-weight-bold">お申込済みカタログ一覧</p>
        <v-divider class="mt-1 mb-7"></v-divider>
        <v-data-table
            class="coverme-member-orders-table"
            :headers="headers"
            :loading="isLoading"
            :items="orders"
            items-per-page="-1"
            hide-default-footer
        >
            <template #[`item.actions`]="{ item }">
                <v-btn @click="openDialog(item)">詳細</v-btn>
            </template>
        </v-data-table>
        <v-dialog v-model="dialog" max-width="500px" @close="closeDialog">
            <the-order
                v-if="selectedOrder"
                :order="selectedOrder as RandselOrder"
            ></the-order>
        </v-dialog>
    </v-container>
</template>

<style scoped></style>
