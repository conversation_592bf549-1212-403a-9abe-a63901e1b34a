<?php

namespace App\Enums\EntityFields;

enum ELogin: string
{
    case LOGIN_PWD = 'password';
    case LOGIN_EMAIL = 'email';
    case TOKEN = 'token';


    /**
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::LOGIN_PWD => 'パスワード',
            self::LOGIN_EMAIL => 'ログインEMAIL',
            self::TOKEN => 'token',
        };
    }
}
