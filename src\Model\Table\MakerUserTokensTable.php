<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\Utility\Security;
use App\Model\Entity\MakerUserToken;

/**
 * メーカーユーザートークンテーブル
 * 
 * @property \App\Model\Table\MakerUsersTable&\Cake\ORM\Association\BelongsTo $MakerUsers
 * @method \App\Model\Entity\MakerUserToken newEmptyEntity()
 * @method \App\Model\Entity\MakerUserToken newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\MakerUserToken[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\MakerUserToken get($primaryKey, $options = [])
 * @method \App\Model\Entity\MakerUserToken findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\MakerUserToken patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\MakerUserToken[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\MakerUserToken|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\MakerUserToken saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 */
class MakerUserTokensTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('maker_user_tokens');
        $this->setDisplayField('token');
        $this->setPrimaryKey('id');

        $this->belongsTo('MakerUsers', [
            'foreignKey' => 'maker_user_id',
            'joinType' => 'INNER',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('id')
            ->allowEmptyString('id', null, 'create');

        $validator
            ->integer('maker_user_id')
            ->requirePresence('maker_user_id', 'create')
            ->notEmptyString('maker_user_id');

        $validator
            ->scalar('token')
            ->maxLength('token', 255)
            ->requirePresence('token', 'create')
            ->notEmptyString('token')
            ->add('token', 'unique', ['rule' => 'validateUnique', 'provider' => 'table']);

        $validator
            ->scalar('type')
            ->maxLength('type', 50)
            ->requirePresence('type', 'create')
            ->notEmptyString('type')
            ->inList('type', [
                MakerUserToken::TYPE_PASSWORD_RESET,
                MakerUserToken::TYPE_API_ACCESS,
                MakerUserToken::TYPE_EMAIL_VERIFICATION
            ]);

        $validator
            ->dateTime('expires')
            ->requirePresence('expires', 'create')
            ->notEmptyDateTime('expires');

        return $validator;
    }

    /**
     * トークンを生成
     */
    public function generateToken(): string
    {
        return bin2hex(Security::randomBytes(32));
    }

    /**
     * 有効なトークンを検索
     */
    public function findValidToken(string $token, string $type = null)
    {
        $query = $this->find()
            ->contain(['MakerUsers'])
            ->where([
                'MakerUserTokens.token' => $token,
                'MakerUserTokens.expires >' => FrozenTime::now()
            ]);

        if ($type) {
            $query->where(['MakerUserTokens.type' => $type]);
        }

        return $query->first();
    }

    /**
     * 期限切れトークンを削除
     */
    public function cleanupExpiredTokens(): int
    {
        return $this->deleteAll([
            'expires <' => FrozenTime::now()
        ]);
    }

    /**
     * ユーザーの特定タイプのトークンを無効化
     */
    public function invalidateUserTokens(int $userId, string $type): int
    {
        return $this->deleteAll([
            'maker_user_id' => $userId,
            'type' => $type
        ]);
    }

    /**
     * パスワードリセットトークンを作成
     */
    public function createPasswordResetToken(int $userId, int $expirationHours = 24)
    {
        // 既存のパスワードリセットトークンを無効化
        $this->invalidateUserTokens($userId, MakerUserToken::TYPE_PASSWORD_RESET);

        $token = $this->newEntity([
            'maker_user_id' => $userId,
            'token' => $this->generateToken(),
            'type' => MakerUserToken::TYPE_PASSWORD_RESET,
            'expires' => FrozenTime::now()->addHours($expirationHours)
        ]);

        return $this->save($token);
    }

    /**
     * 特定のトークンを無効化（削除）
     */
    public function invalidateToken(string $token): bool
    {
        try {
            $tokenEntity = $this->find()
                ->where([
                    'token' => $token,
                    'expires >' => FrozenTime::now()
                ])
                ->first();

            if ($tokenEntity) {
                return (bool)$this->delete($tokenEntity);
            }

            return false;
        } catch (\Exception $e) {
            Log::error("MakerUserTokensTable: Error invalidating token: " . $e->getMessage());
            return false;
        }
    }
}
