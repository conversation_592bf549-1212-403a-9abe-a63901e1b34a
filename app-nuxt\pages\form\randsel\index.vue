<script setup lang="ts">
import { ref, onMounted } from "vue";
import TheSchoolBagForm from "~~/components/form/school-bag/TheSchoolBagForm.vue";
import TheSchoolBagFormLogined from "~~/components/form/school-bag/TheSchoolBagFormLogined.vue";
import { useAsyncData, useRuntimeConfig, navigateTo } from "#app";
import { useAuthStore } from "~~/src/stores/auth";
import Products from "~~/src/models/Products";
import Orders from "~~/src/models/Orders";
import Order from "~~/src/models/entry/Order";
import { useHead } from "unhead";

const isLoading = ref(true);

const authStore = useAuthStore();
const config = useRuntimeConfig();
const { data } = useAsyncData("products", async () => {
    const products = await Products.create(config).getList();
    return {
        products: products.map((t) => t.data),
    };
});
const products = data.value?.products || [];
const isLogined = !!authStore.getAccessToken();
// const member = ref<Member>();
const orders = ref<Order[]>([]);

if (config.public.isProduction) {
    useHead({
        meta: [
            {
                name: "google-site-verification",
                content: "sd6BXRMajQVpR39NUe8I7YxWQyHFFqf0Qvpfx_0xKIQ",
            },
        ],
    });
}

// 初期化処理を行う
onMounted(async () => {
    if (isLogined) {
        try {
            orders.value = await Orders.create(config).index();
            isLoading.value = false;
        } catch (error) {
            console.error("Error fetching orders:", error);
            authStore.clearAuth();
            navigateTo("/member/account/");
        }
    } else {
        isLoading.value = false;
    }
});
</script>

<template>
    <div v-if="!isLoading">
        <div v-if="isLogined">
            <the-school-bag-form-logined
                v-if="products.length > 0 && orders.length > 0"
                :products-data="products"
                :order-data="orders"
            ></the-school-bag-form-logined>
        </div>
        <div v-else>
            <the-school-bag-form
                v-if="products.length"
                :products-data="products"
            ></the-school-bag-form>
        </div>
    </div>
    <div v-else class="coverme-progress-circular">
        <v-progress-circular indeterminate :size="50"></v-progress-circular>
    </div>
</template>

<style scoped>
.coverme-progress-circular {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
